const { ccclass, property } = cc._decorator;
import DirectionType from '../datas/DirectionType';
import CatInfo from '../datas/CatInfo';
import PrefabUtil from '../utils/manager/PrefabUtil';
import GameUtil from '../utils/util/GameUtil';
import UIbase from '../utils/UIbase';
import TipsUI from './TipsUI';
import LocalData from '../manager/LocalData';
import Win<PERSON> from './WinUI';
import FailUI from './FailUI';
import PauseUI from './PauseUI';
import StepOverUI from './StepOverUI';
import TimeOverUI from './TimeOverUI';
import AudioPath from '../datas/AudioPath';
import AudioMgr from '../manager/AudioMgr';
import ChangeGameBgUI from './ChangeGameBgUI';
import { BuffItems } from '../datas/Constants';
import AnimalItem from '../items/AnimalItem';
import buffItemBtn from '../scripts/buffItemBtn';
import ComboUI from './ComboUI';
import mySkinData from '../datas/mySkinData';
import Plat from '../utils/Palt';
import AnimalColorBtn from '../scripts/item/AnimalColorBtn';

@ccclass
export default class GameUI extends UIbase {

    @property([cc.SpriteFrame])
    gameBackgroundArray: cc.SpriteFrame[] = [];


    private static _inst: GameUI;
    isTaskProgressTweening: boolean = false;

    public static get inst() {
        if (this._inst == null) {
            let prefab = PrefabUtil.get("GameUI");
            if (!prefab) {
                console.error("GameUI预制体未找到");
                return null;
            }
            let v = cc.instantiate(prefab);

            this._inst = v.getComponent(GameUI);
        }

        return this._inst;
    }


    @property(cc.Node)
    catContainer: cc.Node = null;

    @property(cc.Node)
    showNode: cc.Node = null;

    @property(cc.Sprite)
    gameBg: cc.Sprite = null;

    @property(cc.Node)
    maskNode: cc.Node = null;  //遮罩

    @property(cc.Node)
    weilanNode: cc.Node = null;  //围栏

    @property(cc.Node)
    buttonsNode: cc.Node = null;  //按钮组

    @property(cc.Node)
    selectNode: cc.Node = null;  //选择视图

    @property(cc.Label)
    timerLabel: cc.Label = null;  //计时器

    @property(cc.Label)
    curLevel: cc.Label = null;  //关卡

    @property(cc.Label)
    stepLabel: cc.Label = null;//步数计数器

    @property(cc.Node)
    taskProgressMask: cc.Node = null; //进度显示

    @property(cc.Label)
    taskProgressLabel: cc.Label = null; //进度显示

    @property(cc.Node)
    comboContainer: cc.Node = null;//连击

    @property(cc.Button)
    autoPlayBtn: cc.Button = null;//自动玩按钮

    private rewardItemPool: cc.NodePool = new cc.NodePool();

    public movementEffectPool: cc.NodePool = new cc.NodePool();

    private taskProgress: number = 0;

    // public grids:Map<number,GridInfo>=new Map();

    public grid_num_list: Array<number> = []

    public grid_map: Map<number, number> = new Map();

    public cat_info_list: Array<CatInfo> = [];

    public horizontal_count = 20;
    public vertical_count = 20;
    public ww = 50;

    public old_cat_pos: Array<CatInfo> = [];

    public is_game_playing = false;
    public is_game_pause = false;

    private stepCount = 0;

    // 自动玩相关变量
    public isAutoPlaying = false;
    private autoPlayTimer: number = null;

    // 全局自动玩状态跟踪（备用方案）
    private static globalAutoPlayState: boolean = false;
    public isRewardSkinLevel = false;

    @property(AnimalColorBtn)
    animalColorBtn: AnimalColorBtn = null;

    start() {

        setInterval(() => {
            this.onTimerUpdate();
        }, 1000);

        let initCount = 5;
        for (let i = 0; i < initCount; ++i) {
            let rewardItemPrefab = PrefabUtil.get("RewardItem");
            if (rewardItemPrefab) {
                let rewardItem = cc.instantiate(rewardItemPrefab); // 创建节点
                this.rewardItemPool.put(rewardItem); // 通过 put 接口放入对象池
            }
        }

        for (let i = 0; i < 10; ++i) {
            let movementEffectPrefab = PrefabUtil.get("MovementEffect");
            if (movementEffectPrefab) {
                let movementEffect = cc.instantiate(movementEffectPrefab); // 创建节点
                this.movementEffectPool.put(movementEffect); // 通过 put 接口放入对象池
            }
        }

        // 检查VIP状态并控制自动玩按钮显示
        this.checkVipStatus();

        // 监听VIP状态变化
        cc.systemEvent.on('vipStatusChanged', this.onVipStatusChanged, this);
    }


    public resetGameData() {
        this.grid_num_list = [];
        this.grid_map = new Map();

        this.cat_info_list = [];

        if (this.taskProgressLabel) {
            this.taskProgressLabel.string = '进度 0%'
        }
        if (this.taskProgressMask) {
            this.taskProgressMask.height = 0;
        }

        if (this.catContainer) {
            this.catContainer.removeAllChildren();
        }

        if (this.maskNode && this.catContainer) {
            this.maskNode.parent = this.catContainer;
        }
        if (this.selectNode) {
            this.selectNode.active = false
        }

    }

    updateBg() {
        if (this.gameBackgroundArray.length > 0) {
            this.gameBackgroundArray.find((frame) => {
                if (frame.name == LocalData.GameBgId && this.gameBg) {
                    this.gameBg.spriteFrame = frame;
                    cc.log(`背景已更新为: ${LocalData.GameBgId}`);
                }
            })
        }
    }

    protected onShow(): void {
        // 确保背景正确显示
        this.updateBg();

        // 确保背景音乐循环播放
        AudioMgr.playBgm();
    }

    private updateStepStr() {
        // 步数设置为无限 - 显示无限符号
        // 检查stepLabel是否存在，避免删除UI后出现错误
        if (this.stepLabel) {
            this.stepLabel.string = "∞"
        }
    }

    public onClickChangeBgBtn() {
        this.is_game_pause = true;
        AudioMgr.playSound(AudioPath.CLICK);
        ChangeGameBgUI.inst.showUI();
    }

    // 自动玩按钮点击处理
    public onClickAutoPlay() {
        AudioMgr.playSound(AudioPath.CLICK);

        if (this.isAutoPlaying) {
            this.stopAutoPlay();
        } else {
            this.startAutoPlay();
        }
    }

    public addStepCount() {
        this.stepCount += 60
        this.updateStepStr()
        this.is_game_playing = true;
    }

    private deleteStepCount() {
        this.stepCount--
        this.updateStepStr()
        // 步数设置为无限 - 注释掉步数检查
        // if (this.stepCount <= 0) {
        //     this.onStepOver()
        // }
    }

    public onTimeOver() {
        this.is_game_playing = false;
        this.stopAutoPlay(); // 停止自动玩
        this.resetComboUi()
        // FailUI.inst.showUI();
        TimeOverUI.inst.showUI()
    }

    public onStepOver() {
        if(this.cat_info_list.length == 0)
        {
            return;
        }
        this.is_game_playing = false;
        this.stopAutoPlay(); // 停止自动玩
        this.resetComboUi()
        StepOverUI.inst.showUI()
    }


    public onStartGame() {

        this.is_game_pause = false;
          Plat.hidemoban();
        // Plat.showInterstitialAd()
        this.resetGameData();

        this.showUI();

        // 初始化背景
        this.updateBg();

        // 确保背景音乐循环播放
        AudioMgr.playBgm();

        this.randomCatInfos();

        this.updateUI();

        this.scheduleOnce(() => {
            this.checkShow();
            //进度展示初始化
            this.taskProgress = this.cat_info_list.length
            // console.log(this.cat_info_list.length)
        }, 0.1)

        if (this.curLevel) {
            this.curLevel.string = "第 " + LocalData.lv + " 关";
        }

        this.updateTimeLabel();

        if (this.animalColorBtn) {
            this.animalColorBtn.clearDataKeepMode(); // 保持当前颜色模式状态
        }

        // 不要重置自动玩状态，让WinUI的自动切换功能继续工作
        // 只有在非自动玩模式下才重置
        // this.stopAutoPlay(); // 注释掉这行，保持自动玩状态
    }

    private setLvData() {
        let lv = LocalData.lv;

        if (lv == 1) {
            this.vertical_count = 6;
            this.horizontal_count = 6;

            if (this.catContainer) {
                this.catContainer.scale = 1.5;
            }

            this.time = 60;
        }
        else if (lv % 2 == 0) {
            this.isRewardSkinLevel = false;
            let randomNumber = GameUtil.randomRange(12, 20)
            this.vertical_count = randomNumber;
            this.horizontal_count = randomNumber;
            if (this.catContainer) {
                this.catContainer.scale = 1;
            }

            this.time = 210;
        }
        else {
            if(mySkinData.findMinOrderElementNotInSkins() != null)
            {
                this.isRewardSkinLevel = true;
            }
            this.vertical_count = 20;
            this.horizontal_count = 20;

            this.time = 210;

            // this.catContainer.scale = 0.8;
            if (this.catContainer) {
                this.catContainer.scale = 1;
            }

        }
    }


    private dirs: Array<number> = [1, 2, 3, 4];

    public randomCatInfos() {
        this.setLvData();

        this.cat_info_list = [];

        for (let i = 0; i < this.vertical_count; i++) {
            for (let k = 0; k < this.horizontal_count; k++) {
                this.grid_num_list.push(i * 10000 + k);
                this.grid_map.set(i * 10000 + k, 0);
            }
        }

        GameUtil.randomRangeSort(this.grid_num_list);

        let index = 0;


        while (true) {
            if (this.grid_num_list.length == 0) {
                break;
            }

            GameUtil.randomRangeSort(this.dirs);

            let flag = false;

            if (GameUtil.randomRange(0, 100) < 50) {
                for (let i = 0; i < 4; i++) {
                    let id = this.grid_num_list[0];

                    let id2 = this.getNextGrid(id, this.dirs[i]);

                    if (id2 == -1) {
                        continue;
                    }

                    if (this.grid_map.get(id2) == 1) {
                        continue;
                    }

                    let info = new CatInfo();
                    info.dir = this.dirs[i];
                    info.now_grids.push(id);
                    info.now_grids.push(id2);

                    this.grid_map.set(id, 1);
                    this.grid_map.set(id2, 1);

                    GameUtil.deleteValue(this.grid_num_list, id);
                    GameUtil.deleteValue(this.grid_num_list, id2);

                    this.cat_info_list.push(info);

                    flag = true;

                    break;
                }
            }

            if (flag == false) {
                // console.warn("咋回事呀");
                this.grid_num_list.shift();
            }

            index++;

            if (index > 10000) {
                break;
            }
        }


        if (LocalData.lv == 1 && this.canAllCatOut() == false) {
            this.randomCatInfos();
        }

        if (this.cat_info_list.length < 4) {
            this.randomCatInfos();
        }

    }

    public getNextGrid(id: number, dir: number) {
        let i = Math.floor(id / 10000);
        let k = id % 10000;

        if (dir == DirectionType.up) {
            k--;
        }
        else if (dir == DirectionType.down) {
            k++;
        }
        else if (dir == DirectionType.left) {
            i--;
        }
        else if (dir == DirectionType.right) {
            i++;
        }

        let val = i * 10000 + k;

        if (this.grid_map.has(val) == true) {
            return val;
        }
        else {
            return -1;
        }
    }

    public updateUI() {
        if (!this.catContainer) {
            console.warn("catContainer is null, cannot display animals");
            return;
        }

        for (let i = 0; i < this.cat_info_list.length; i++) {
            let cat = this.getCatNode(this.cat_info_list[i]);
            cat.parent = this.catContainer;

            cat.on(cc.Node.EventType.TOUCH_END, this.onClickCatNode, this)

            this.cat_info_list[i].cat = cat;
            cat.opacity = 0;
        }

        if (this.isRewardSkinLevel == false) {
            if (this.taskProgressMask) {
                this.taskProgressMask.parent.active = false
            }
        } else {
            if (this.taskProgressMask) {
                this.taskProgressMask.parent.active = true
            }
        }

        if (this.weilanNode) {
            if (this.vertical_count == 6) {
                this.weilanNode.width = 650;
                this.weilanNode.height = 800;
            } else {
                this.weilanNode.width = 750;
                this.weilanNode.height = 1075;
            }
        }
    }

    public getCatNode(info: CatInfo): cc.Node {

        let node: cc.Node;

        if (info.cat == null) {
            let animalItemPrefab = PrefabUtil.get("AnimalItem");
            if (animalItemPrefab) {
                node = cc.instantiate(animalItemPrefab);
                // console.warn("创建的羊---"+(<any>node)._id)
            } else {
                console.error("AnimalItem预制体未找到");
                return null;
            }
        }
        else {
            node = info.cat;
        }


        let x = Math.floor(info.now_grids[0] / 10000);
        let y = info.now_grids[0] % 10000;

        x = (x * this.ww - this.ww / 2 * this.vertical_count) + this.ww / 2;

        y = (this.ww / 2 * this.horizontal_count - y * this.ww) - this.ww / 2;

        if (info.dir == DirectionType.up) {
            y += this.ww / 2;
            node.angle = 0

        }
        else if (info.dir == DirectionType.down) {
            y -= this.ww / 2;

            node.angle = 180
        }
        else if (info.dir == DirectionType.left) {
            x -= this.ww / 2;

            node.angle = 90
        }
        else if (info.dir == DirectionType.right) {
            x += this.ww / 2;

            node.angle = 270
        }



        node.x = x;
        node.y = y;

        return node;

    }

    public onClickCatNode(event: cc.Event.EventTouch) {
        AudioMgr.playSound(AudioPath.SHEEP);
        if (this.is_game_playing == false) {
            return;
        }

        this.maskNode.active = false;
        for (let i = 0; i < this.cat_info_list.length; i++) {
            this.cat_info_list[i].cat.zIndex = 0;
        }


        //   console.warn("被点击的羊---"+(<any>event.target)._id)

        for (let i = 0; i < this.cat_info_list.length; i++) {
            //   console.warn("被查询的羊---"+(<any>this.yangs[i].cat)._id)

            if (this.cat_info_list[i].cat == event.target) {
                //  console.warn(this.yangs[i].grids[0]+"的羊被点击了");

                if (this.fz_flag == true) {
                    console.log("AAA");
                    
                    this.resetDirection2(this.cat_info_list[i]);
                }
                else if(this.xiaochu_flag == true){
                    console.log("AAA2");
                    this.xiaochuayang(this.cat_info_list[i]);
                }
                else {
                    this.onMoveCat(this.cat_info_list[i]);
                }

                return;
            }
        }


    }
    public xiaochuayang(info:CatInfo){
         if (this.cat_info_list.length == 0) {
            return;
        }

        if (this.buttonsNode) {
            this.buttonsNode.active = true;
        }

        this.xiaochu_flag = false;

        if (this.selectNode) {
            this.selectNode.active = false;
        }

        // this.resetDirection3(info);
        this.xiaochuayangcao(info);
    }
    public xiaochuayangcao(info:CatInfo){

        //  info.cat = this.getCatNode(info);
          //可以消失
          let delay = 1;
             if (info.dir == DirectionType.up) {
         
                //可以消失
                info.move_tween = cc.tween(info.cat)
                    .parallel(
                        cc.tween().by(0.1, { y: 15 }).by(2, { y: 2000 }),
                        cc.tween().call(() => {
                            this.createTrailEffect(info)
                        }),
                        cc.tween().delay(delay).call(() => {
                            this.showTaskProgressAfterMovement(info)
                        })
                    )
                    .call(() => {
                        // console.warn("向上移出完成");
                        info.move_tween = null;
                        info.lastPosition = null;
                    })
                    .start();

                this.removeCat(info);

           
        }
        else if (info.dir == DirectionType.down) {
         
                //可以消失
                info.move_tween = cc.tween(info.cat)
                    .parallel(
                        cc.tween().to(0.1, { y: info.cat.y - 15 })
                            .to(2, { y: info.cat.y - 2000 }),
                        cc.tween().call(() => {
                            this.createTrailEffect(info)
                        }),
                        cc.tween().delay(delay).call(() => {
                            this.showTaskProgressAfterMovement(info)
                        })
                    )
                    .call(() => {
                        // console.warn("向上移出完成");
                        info.move_tween = null;
                        info.lastPosition = null;

                    })
                    .start();

                this.removeCat(info);

           
        }
        else if (info.dir == DirectionType.left) {
            
                //可以消失
                info.move_tween = cc.tween(info.cat)
                    .parallel(
                        cc.tween().to(0.1, { x: info.cat.x - 15 })
                            .to(2, { x: info.cat.x - 2000 }),
                        cc.tween().call(() => {
                            this.createTrailEffect(info)
                        }),
                        cc.tween().delay(delay).call(() => {
                            this.showTaskProgressAfterMovement(info)
                        })
                    )

                    .call(() => {
                        // console.warn("向上移出完成");
                        info.move_tween = null;
                        info.lastPosition = null;
                    })
                    .start();

                this.removeCat(info);
           
        }
        else if (info.dir == DirectionType.right) {


           

                //可以消失
                info.move_tween = cc.tween(info.cat)
                    .parallel(
                        cc.tween().to(0.1, { x: info.cat.x + 15 })
                            .to(2, { x: info.cat.x + 2000 }),
                        cc.tween().call(() => {
                            this.createTrailEffect(info)
                        }),
                        cc.tween().delay(delay).call(() => {
                            this.showTaskProgressAfterMovement(info)
                        })
                    )
                    .call(() => {
                        // console.warn("向上移出完成");
                        info.move_tween = null;
                        info.lastPosition = null;
                    })
                    .start();

                this.removeCat(info);

            
           
        }
    }
    //检测出来移动位置
    public onMoveCat(info: CatInfo) {

        for (let i = 0; i < info.now_grids.length; i++) {
            this.grid_map.set(info.now_grids[i], 0);
        }

        let i = Math.floor(info.now_grids[0] / 10000);
        let k = info.now_grids[0] % 10000;

        // 调试信息：显示动物当前状态
        let directionName = "";
        switch(info.dir) {
            case DirectionType.up: directionName = "向上"; break;
            case DirectionType.down: directionName = "向下"; break;
            case DirectionType.left: directionName = "向左"; break;
            case DirectionType.right: directionName = "向右"; break;
        }
        cc.log(`动物移动：方向=${directionName}(${info.dir}), 角度=${info.cat.angle}, 网格位置=(${i},${k})`);

        if (info.move_tween && info.lastPosition) {
            info.move_tween.stop()
            info.cat.x = info.lastPosition.x;
            info.cat.y = info.lastPosition.y;
        }
        info.lastPosition = new cc.Vec2(info.cat.x, info.cat.y)
        const startX = info.cat.x;
        const startY = info.cat.y;
        let add = 0;
        

        let delay = 1;
        let movementEffectDelay = 0.1
        if (info.dir == DirectionType.up) {
            k -= 1;
            cc.log(`动物向上移动：起始位置 i=${i}, k=${k}, 网格边界 horizontal_count=${this.horizontal_count}`);

            while (true) {
                add += 1;
                let nextK = k - add;
                let nextGridId = i * 10000 + nextK;

                // 检查是否超出上边界
                if (nextK < 0) {
                    cc.log(`动物向上移动：超出上边界，nextK=${nextK} < 0，可以离开`);
                    add = 100;
                    break;
                }

                // 检查网格是否存在
                if (!this.grid_map.has(nextGridId)) {
                    cc.log(`动物向上移动：网格不存在，gridId=${nextGridId}，可以离开`);
                    add = 100;
                    break;
                }

                // 检查网格是否被占用
                if (this.grid_map.get(nextGridId) == 1) {
                    add -= 1;
                    cc.log(`动物向上移动：碰撞到障碍物，gridId=${nextGridId}，移动距离=${add}`);
                    break;
                }
            }

            if (add != 0) {
                info.push_last();
                this.old_cat_pos.push(info);
            }

            if (add == 100) {
                //可以消失
                info.move_tween = cc.tween(info.cat)
                    .parallel(
                        cc.tween().by(0.1, { y: 15 }).by(2, { y: 2000 }),
                        cc.tween().call(() => {
                            this.createTrailEffect(info)
                        }),
                        cc.tween().delay(delay).call(() => {
                            this.showTaskProgressAfterMovement(info)
                        })
                    )
                    .call(() => {
                        // console.warn("向上移出完成");
                        info.move_tween = null;
                        info.lastPosition = null;
                    })
                    .start();

                this.removeCat(info);

            }
            else {
                //碰撞
                info.move_tween = cc.tween(info.cat)
                    .to(0.1, { y: info.cat.y + 15 })
                    .to(0.1 * add, { y: info.cat.y + 15 + this.ww * add })
                    .delay(0.1)
                    .to(0.1, { y: info.cat.y + 15 + this.ww * add - 15 })
                    .call(() => {
                        this.onAnimalCollison(info)
                        // console.warn("向上移出完成");
                        info.move_tween = null;
                        info.lastPosition = null;
                    })
                    .start();


                for (let i = 0; i < info.now_grids.length; i++) {
                    let a = Math.floor(info.now_grids[i] / 10000);
                    let b = info.now_grids[i] % 10000;

                    b = b - add;

                    info.now_grids[i] = a * 10000 + b;

                    this.grid_map.set(info.now_grids[i], 1);
                }


            }
        }
        else if (info.dir == DirectionType.down) {
            k += 1;
            cc.log(`动物向下移动：起始位置 i=${i}, k=${k}, 网格边界 horizontal_count=${this.horizontal_count}`);

            while (true) {
                add += 1;
                let nextK = k + add;
                let nextGridId = i * 10000 + nextK;

                // 检查是否超出下边界
                if (nextK >= this.horizontal_count) {
                    cc.log(`动物向下移动：超出下边界，nextK=${nextK} >= ${this.horizontal_count}，可以离开`);
                    add = 100;
                    break;
                }

                // 检查网格是否存在
                if (!this.grid_map.has(nextGridId)) {
                    cc.log(`动物向下移动：网格不存在，gridId=${nextGridId}，可以离开`);
                    add = 100;
                    break;
                }

                // 检查网格是否被占用
                if (this.grid_map.get(nextGridId) == 1) {
                    add -= 1;
                    cc.log(`动物向下移动：碰撞到障碍物，gridId=${nextGridId}，移动距离=${add}`);
                    break;
                }
            }

            if (add != 0) {
                info.push_last();
                this.old_cat_pos.push(info);
            }

            if (add == 100) {

                //可以消失
                info.move_tween = cc.tween(info.cat)
                    .parallel(
                        cc.tween().to(0.1, { y: info.cat.y - 15 })
                            .to(2, { y: info.cat.y - 2000 }),
                        cc.tween().call(() => {
                            this.createTrailEffect(info)
                        }),
                        cc.tween().delay(delay).call(() => {
                            this.showTaskProgressAfterMovement(info)
                        })
                    )
                    .call(() => {
                        // console.warn("向上移出完成");
                        info.move_tween = null;
                        info.lastPosition = null;

                    })
                    .start();

                this.removeCat(info);

            }
            else {
                //碰撞
                info.move_tween = cc.tween(info.cat)
                    .to(0.1, { y: info.cat.y - 15 })
                    .to(0.1 * add, { y: info.cat.y - 15 - this.ww * add })
                    .delay(0.1)
                    .to(0.1, { y: info.cat.y - 15 - this.ww * add + 15 })
                    .call(() => {
                        this.onAnimalCollison(info)
                        // console.warn("向下移出完成");
                        info.move_tween = null;
                        info.lastPosition = null;
                    })
                    .start();


                for (let i = 0; i < info.now_grids.length; i++) {
                    let a = Math.floor(info.now_grids[i] / 10000);
                    let b = info.now_grids[i] % 10000;

                    b = b + add;

                    info.now_grids[i] = a * 10000 + b;

                    this.grid_map.set(info.now_grids[i], 1);
                }

            }
        }
        else if (info.dir == DirectionType.left) {
            i -= 1;
            cc.log(`动物向左移动：起始位置 i=${i}, k=${k}, 网格边界 vertical_count=${this.vertical_count}`);

            while (true) {
                add += 1;
                let nextI = i - add;
                let nextGridId = nextI * 10000 + k;

                // 检查是否超出左边界
                if (nextI < 0) {
                    cc.log(`动物向左移动：超出左边界，nextI=${nextI} < 0，可以离开`);
                    add = 100;
                    break;
                }

                // 检查网格是否存在
                if (!this.grid_map.has(nextGridId)) {
                    cc.log(`动物向左移动：网格不存在，gridId=${nextGridId}，可以离开`);
                    add = 100;
                    break;
                }

                // 检查网格是否被占用
                if (this.grid_map.get(nextGridId) == 1) {
                    add -= 1;
                    cc.log(`动物向左移动：碰撞到障碍物，gridId=${nextGridId}，移动距离=${add}`);
                    break;
                }
            }

            if (add != 0) {
                info.push_last();
                this.old_cat_pos.push(info);
            }

            if (add == 100) {
                //可以消失
                info.move_tween = cc.tween(info.cat)
                    .parallel(
                        cc.tween().to(0.1, { x: info.cat.x - 15 })
                            .to(2, { x: info.cat.x - 2000 }),
                        cc.tween().call(() => {
                            this.createTrailEffect(info)
                        }),
                        cc.tween().delay(delay).call(() => {
                            this.showTaskProgressAfterMovement(info)
                        })
                    )

                    .call(() => {
                        // console.warn("向上移出完成");
                        info.move_tween = null;
                        info.lastPosition = null;
                    })
                    .start();

                this.removeCat(info);
            }
            else {
                //碰撞
                info.move_tween = cc.tween(info.cat)
                    .to(0.1, { x: info.cat.x - 15 })
                    .to(0.1 * add, { x: info.cat.x - 15 - this.ww * add })
                    .delay(0.1)
                    .to(0.1, { x: info.cat.x - 15 - this.ww * add + 15 })
                    .call(() => {
                        this.onAnimalCollison(info)
                        // console.warn("向上移出完成");
                        info.move_tween = null;
                    })
                    .start();


                for (let i = 0; i < info.now_grids.length; i++) {

                    let a = Math.floor(info.now_grids[i] / 10000);
                    let b = info.now_grids[i] % 10000;

                    a = a - add;

                    info.now_grids[i] = a * 10000 + b;

                    this.grid_map.set(info.now_grids[i], 1);
                }


            }
        }
        else if (info.dir == DirectionType.right) {
            i += 1;
            cc.log(`动物向右移动：起始位置 i=${i}, k=${k}, 网格边界 vertical_count=${this.vertical_count}`);

            while (true) {
                add += 1;
                let nextI = i + add;
                let nextGridId = nextI * 10000 + k;

                // 检查是否超出右边界
                if (nextI >= this.vertical_count) {
                    cc.log(`动物向右移动：超出右边界，nextI=${nextI} >= ${this.vertical_count}，可以离开`);
                    add = 100;
                    break;
                }

                // 检查网格是否存在
                if (!this.grid_map.has(nextGridId)) {
                    cc.log(`动物向右移动：网格不存在，gridId=${nextGridId}，可以离开`);
                    add = 100;
                    break;
                }

                // 检查网格是否被占用
                if (this.grid_map.get(nextGridId) == 1) {
                    add -= 1;
                    cc.log(`动物向右移动：碰撞到障碍物，gridId=${nextGridId}，移动距离=${add}`);
                    break;
                }
            }

            if (add != 0) {
                info.push_last();
                this.old_cat_pos.push(info);
            }

            if (add == 100) {

                //可以消失
                info.move_tween = cc.tween(info.cat)
                    .parallel(
                        cc.tween().to(0.1, { x: info.cat.x + 15 })
                            .to(2, { x: info.cat.x + 2000 }),
                        cc.tween().call(() => {
                            this.createTrailEffect(info)
                        }),
                        cc.tween().delay(delay).call(() => {
                            this.showTaskProgressAfterMovement(info)
                        })
                    )
                    .call(() => {
                        // console.warn("向上移出完成");
                        info.move_tween = null;
                        info.lastPosition = null;
                    })
                    .start();

                this.removeCat(info);

            }
            else {
                //碰撞
                info.move_tween = cc.tween(info.cat)
                    .to(0.1, { x: info.cat.x + 15 })
                    .to(0.1 * add, { x: info.cat.x + 15 + this.ww * add })
                    .delay(0.1)
                    .to(0.1, { x: info.cat.x + 15 + this.ww * add - 15 })
                    .call(() => {
                        this.onAnimalCollison(info)
                        // console.warn("向下移出完成");
                        info.move_tween = null;
                        info.lastPosition = null;
                    })
                    .start();


                for (let i = 0; i < info.now_grids.length; i++) {
                    let a = Math.floor(info.now_grids[i] / 10000);
                    let b = info.now_grids[i] % 10000;

                    a = a + add;

                    info.now_grids[i] = a * 10000 + b;

                    this.grid_map.set(info.now_grids[i], 1);
                }

            }
        }

        this.deleteStepCount()
    }

    createTrailEffect(info: CatInfo) {
        if (info.cat) {
            let script: AnimalItem = info.cat.getComponent('AnimalItem')

            if (script) {
                script.createTrailEffect()
            }
        }
    }

    showTaskProgressAfterMovement(info: CatInfo) {

        if (this.isRewardSkinLevel == false) {
            return;
        }

        let tempVe3 = new cc.Vec3()
        let animalEndPosition = new cc.Vec3()

        info.cat.getPosition(tempVe3)
        info.cat.parent.convertToWorldSpaceAR(tempVe3, animalEndPosition)

        if (this.taskProgressMask == null) {
            return;
        }

        this.taskProgressMask.getPosition(tempVe3)

        let targetPosition = new cc.Vec3()
        this.taskProgressMask.parent.convertToWorldSpaceAR(tempVe3, targetPosition)

        // 1. 从对象池中获取节点
        let node: cc.Node;
        if (this.rewardItemPool.size() > 0) {
            node = this.rewardItemPool.get();
        } else {
            let rewardItemPrefab = PrefabUtil.get('RewardItem');
            if (rewardItemPrefab) {
                node = cc.instantiate(rewardItemPrefab);
            } else {
                console.error("RewardItem预制体未找到");
                return;
            }
        }

        if (this.catContainer) {
            this.catContainer.addChild(node);
            node.setPosition(this.catContainer.convertToNodeSpaceAR(animalEndPosition));
        }

        // 3. 执行动作
        cc.tween(node)
            .to(0.6, { position: node.parent.convertToNodeSpaceAR(targetPosition) }) // 0.6 是移动时间，调快了移动速度
            .call(() => {
                node.removeFromParent(); // 移动完成后移除节点
                this.rewardItemPool.put(node); // 将节点放回对象池
                this.updateTaskProgressAfterReward(this.cat_info_list.length) //更新进度提示

            })
            .start();

    }

    updateTaskProgressAfterReward(animals: number) {
        let progressPercent: number = 0;
        progressPercent = ((this.taskProgress - animals) / this.taskProgress);
        if (this.taskProgressLabel) {
            this.taskProgressLabel.string = '进度 ' + (progressPercent * 100).toFixed(2) + '%'
        }

        if (this.taskProgressMask) {
            let ProgressShadowAnimalIcon = this.taskProgressMask.children[0]
            if (ProgressShadowAnimalIcon) {
                let high = ProgressShadowAnimalIcon.height * ProgressShadowAnimalIcon.scaleY

                cc.tween(this.taskProgressMask).to(1, { height: high * progressPercent }).call(() => {
                    if (progressPercent == 1) {
                        this.isTaskProgressTweening = false;
                    }
                })
                    .start()
            }
        }
    }

    public removeCat(info: CatInfo) {
        if (this.isRewardSkinLevel == true) {
            this.isTaskProgressTweening = true;
        }

        // 清理网格状态 - 这是关键修复！
        for (let a = 0; a < info.now_grids.length; a++) {
            this.grid_map.set(info.now_grids[a], 0);
        }

        for (let i = 0; i < this.cat_info_list.length; i++) {
            if (this.cat_info_list[i] == info) {
                // console.warn("删除的羊---" + (<any>this.cat_info_list[i].cat)._id)
                this.combo()
                this.cat_info_list.splice(i, 1);
                break; // 找到后立即退出循环
            }
        }

        console.log(`自动玩：动物已移除，清理了${info.now_grids.length}个网格位置`);
    }

    combo() {

        if (this.comboContainer) {
            let combo = this.comboContainer.getComponent('ComboUI');

            if (combo) {
                combo.updateProgressBar()
            }
        }

    }

    resetComboUi()
    {
        if (this.comboContainer) {
            let combo = this.comboContainer.getComponent('ComboUI');

            if (combo) {
                combo.resetUi()
            }
        }
    }

    //检测展示
    public checkShow() {
        let arr = [];

        let show_fw = this.node.convertToWorldSpaceAR(new cc.Vec2(0, 0));
        let h = this.showNode.height;
        let w = this.showNode.width;
        if(cc.sys.getSafeAreaRect())
        {
            h = cc.sys.getSafeAreaRect().height * 0.7;
            w = cc.sys.getSafeAreaRect().width * 0.9;
        }

        //在编辑器里调节Node的宽高之前的高是920，显示太密集 难度
        let down = show_fw.y - h / 2 - 50;
        let up = show_fw.y + h / 2 - 50;
        let left = show_fw.x - w / 2;
        let right = show_fw.x + w / 2;
 
        for (let i = 0; i < this.cat_info_list.length; i++) {
            let cat = this.cat_info_list[i];

            let curr_pos = cat.cat.parent.convertToWorldSpaceAR(cat.cat.position);

            if (curr_pos.x < left) {
                arr.push(cat);
                continue;
            }

            if (curr_pos.x > right) {
                arr.push(cat);
                continue;
            }

            if (curr_pos.y < down) {
                arr.push(cat);
                continue;
            }

            if (curr_pos.y > up) {
                arr.push(cat);
                continue;
            }
        }

        for (let i = 0; i < arr.length; i++) {
            for (let k = 0; k < this.cat_info_list.length; k++) {
                if (this.cat_info_list[k] == arr[i]) {
                    this.cat_info_list[k].cat.parent = null;

                    for (let a = 0; a < this.cat_info_list[k].now_grids.length; a++) {
                        this.grid_map.set(this.cat_info_list[k].now_grids[a], 0);
                    }

                    //   console.warn("不展示的羊---"+(<any>this.yangs[k].cat)._id)

                    this.cat_info_list.splice(k, 1);

                    break;
                }
            }
        }


        this.checkDirection();

        for (let i = 0; i < this.cat_info_list.length; i++) {
            let cat = this.cat_info_list[i];
            cat.cat.scale = 0;
            cat.cat.opacity = 255;

            cc.tween(cat.cat)
                .delay(GameUtil.randomRange(100, 1500) / 1000)
                .to(0.5, { scale: 1 })
                .start();
        }

        this.is_game_playing = true;
        this.stepCount = this.cat_info_list.length + 10
        this.updateStepStr()
    }


    //检测可以跑出去的羊
    public chatCatIsOut(): Array<CatInfo> {
        let arr: Array<CatInfo> = [];

        for (let a = 0; a < this.cat_info_list.length; a++) {
            let info = this.cat_info_list[a];

            let i = Math.floor(info.now_grids[0] / 10000);
            let k = info.now_grids[0] % 10000;

            if (this.grid_map.get(info.now_grids[0]) == 0) {
                continue;
            }

            let add = 0;

            if (info.dir == DirectionType.up) {
                k -= 1;

                while (true) {
                    add += 1;

                    if (k - add < 0) {
                        add = 100;
                        break;
                    }

                    if (this.grid_map.get(i * 10000 + (k - add)) == 1) {
                        add -= 1;
                        break;
                    }
                }

                if (add == 100) {
                    arr.push(info);
                }

            }
            else if (info.dir == DirectionType.down) {
                k += 1;

                while (true) {
                    add += 1;

                    if (k + add >= this.horizontal_count) {
                        add = 100;
                        break;
                    }

                    if (this.grid_map.get(i * 10000 + (k + add)) == 1) {
                        add -= 1;
                        break;
                    }
                }

                if (add == 100) {
                    arr.push(info);
                }

            }
            else if (info.dir == DirectionType.left) {
                i -= 1;

                while (true) {
                    add += 1;

                    if (i - add < 0) {
                        add = 100;
                        break;
                    }

                    if (this.grid_map.get((i - add) * 10000 + k) == 1) {
                        add -= 1;
                        break;
                    }
                }

                if (add == 100) {
                    arr.push(info);

                }

            }
            else if (info.dir == DirectionType.right) {
                i += 1;

                while (true) {
                    add += 1;

                    if (i + add >= this.vertical_count) {
                        add = 100;
                        break;
                    }

                    if (this.grid_map.get((i + add) * 10000 + k) == 1) {
                        add -= 1;
                        break;
                    }
                }

                if (add == 100) {
                    arr.push(info);
                }
            }
        }

        return arr;
    }


    public canAllCatOut() {

        let jilu: Array<number> = [];

        while (true) {
            let aa = this.chatCatIsOut();

            if (aa.length == 0) {
                // console.error("不可全部消除");
                break;
            }

            for (let i = 0; i < aa.length; i++) {
                for (let k = 0; k < aa[i].now_grids.length; k++) {
                    jilu.push(aa[i].now_grids[k]);
                    this.grid_map.set(aa[i].now_grids[k], 0);
                }
            }

            if (jilu.length == this.cat_info_list.length * 2) {
                // console.error("可以全部消除")

                for (let i = 0; i < jilu.length; i++) {
                    this.grid_map.set(jilu[i], 1);
                }

                return true;
            }
        }

        return false;

    }


    //撤回
    public onClickRevoke() {
        if (this.old_cat_pos.length == 0) {
            TipsUI.inst.showTips("没有可撤回的操作");
            return;
        }

        let info = this.old_cat_pos.pop();

        if (info.move_tween != null) {
            info.move_tween.stop();
        }

        for (let i = 0; i < info.now_grids.length; i++) {
            this.grid_map.set(info.now_grids[i], 0);
        }

        info.revoke();

        for (let i = 0; i < info.now_grids.length; i++) {
            this.grid_map.set(info.now_grids[i], 1);
        }

        info.cat = this.getCatNode(info);

        if (this.cat_info_list.indexOf(info) == -1) {
            this.cat_info_list.push(info);
        }
        // this.is_game_playing = false
        // Plat.showRewardVideo((success: boolean) => {
        //     this.is_game_playing = true;
        //     if (success) {
        //         if (this.old_cat_pos.length == 0) {
        //             TipsUI.inst.showTips("没有可撤回的操作");
        //             return;
        //         }

        //         let info = this.old_cat_pos.pop();

        //         if (info.move_tween != null) {
        //             info.move_tween.stop();
        //         }

        //         for (let i = 0; i < info.now_grids.length; i++) {
        //             this.grid_map.set(info.now_grids[i], 0);
        //         }

        //         info.revoke();

        //         for (let i = 0; i < info.now_grids.length; i++) {
        //             this.grid_map.set(info.now_grids[i], 1);
        //         }

        //         info.cat = this.getCatNode(info);

        //         if (this.cat_info_list.indexOf(info) == -1) {
        //             this.cat_info_list.push(info);
        //         }
        //     }
        // })
    }

    //高亮
    public onClickLightCat() {
        let arr = this.chatCatIsOut();

        if (arr.length == 0) {
            TipsUI.inst.showTips("没有可消除的动物");
            return;
        }

        this.maskNode.active = true;

        this.maskNode.zIndex = 99;

        for (let i = 0; i < arr.length; i++) {
            arr[i].cat.zIndex = 100;
        }
        // this.is_game_playing = false;
        // Plat.showRewardVideo((success: boolean) => {
        //     this.is_game_playing = true;
        //     if (success) {
        //         let arr = this.chatCatIsOut();

        //         if (arr.length == 0) {
        //             TipsUI.inst.showTips("没有可消除的动物");
        //             return;
        //         }

        //         this.maskNode.active = true;

        //         this.maskNode.zIndex = 99;

        //         for (let i = 0; i < arr.length; i++) {
        //             arr[i].cat.zIndex = 100;
        //         }
        //     }
        // })
    }

    public fz_flag = false;
    public onClickResetDirection() {

        if (this.cat_info_list.length == 0) {
            return;
        }

        if (this.buttonsNode) {
            this.buttonsNode.active = false;
        }

        this.fz_flag = true;

        if (this.selectNode) {
            this.selectNode.active = true;
        }

        for (let i = 0; i < this.cat_info_list.length; i++) {
            this.cat_info_list[i].cat.zIndex = 100;
        }
        // this.is_game_playing = false
        // Plat.showRewardVideo((success: boolean) => {
        //     this.is_game_playing = true
        //     if (success) {
        //         if (this.cat_info_list.length == 0) {
        //             return;
        //         }

        //         this.buttonsNode.active = false;

        //         this.fz_flag = true;

        //         this.selectNode.active = true;

        //         for (let i = 0; i < this.cat_info_list.length; i++) {
        //             this.cat_info_list[i].cat.zIndex = 100;
        //         }
        //     }
        // })

    }
xiaochu_flag = false;
    public onClickAddTime() {
        console.log("这里是消除操作");
        if (this.cat_info_list.length == 0) {
            return;
        }

        if (this.buttonsNode) {
            this.buttonsNode.active = false;
        }

        this.xiaochu_flag = true;

        if (this.selectNode) {
            this.selectNode.active = true;
        }

        for (let i = 0; i < this.cat_info_list.length; i++) {
            this.cat_info_list[i].cat.zIndex = 100;
        }
        // this.time += 30;
        // this.updateTimeLabel();
    }

    public resetDirection2(info: CatInfo) {
        if (this.cat_info_list.length == 0) {
            return;
        }

        if (this.buttonsNode) {
            this.buttonsNode.active = true;
        }

        this.fz_flag = false;

        if (this.selectNode) {
            this.selectNode.active = false;
        }

        this.resetDirection3(info);
    }

    public resetDirection3(info: CatInfo) {
        info.dir = (info.dir - 1 + 2) % 4 + 1;
        info.now_grids.reverse();

        // 不重新创建节点，只更新位置和角度
        if (info.cat) {
            let result = this.getCatPositionAndAngle(info);
            info.cat.x = result.pos.x;
            info.cat.y = result.pos.y;
            info.cat.angle = result.angle;
        } else {
            // 如果节点不存在才创建
            info.cat = this.getCatNode(info);
        }
    }

    public time = 240;

    private onTimerUpdate() {
        if (this.is_game_playing == false || this.cat_info_list.length == 0) {
            return;
        }

        if (this.is_game_pause == true) {
            return;
        }


        // this.time--;
  

        this.updateTimeLabel();
    }

    public updateTimeLabel() {
  

    }

    private checkDirection() {

        let map: Map<number, Array<CatInfo>> = new Map();

        let map2: Map<number, Array<CatInfo>> = new Map();

        for (let i = 0; i < this.cat_info_list.length; i++) {
            let info = this.cat_info_list[i];

            if (info.dir == DirectionType.left || info.dir == DirectionType.right) {
                let g = Math.floor(info.now_grids[0] % 10000);

                if (info.dir == DirectionType.right) {
                    this.resetDirection3(info);
                }

                if (map.has(g) == false) {
                    map.set(g, new Array());
                }

                map.get(g).push(info);
            }
            else {

                if (info.dir == DirectionType.down) {
                    this.resetDirection3(info);
                }

                let g = Math.floor(info.now_grids[0] / 10000);

                if (map2.has(g) == false) {
                    map2.set(g, new Array());
                }

                map2.get(g).push(info);

            }
        }

        map.forEach((val: Array<CatInfo>, key) => {

            val.sort((v1, v2) => {

                let a1 = Math.floor(v1.now_grids[0] / 10000);
                let a2 = Math.floor(v2.now_grids[0] / 10000);

                return a1 - a2;
            })

            let index = GameUtil.randomRange(0, val.length + 1);

            for (let i = index; i < val.length; i++) {
                this.resetDirection3(val[i]);
            }


        });

        map2.forEach((val: Array<CatInfo>, key) => {

            val.sort((v1, v2) => {

                let a1 = Math.floor(v1.now_grids[0] % 10000);
                let a2 = Math.floor(v2.now_grids[0] % 10000);

                return a1 - a2;
            })

            let index = GameUtil.randomRange(0, val.length + 1);

            for (let i = index; i < val.length; i++) {
                this.resetDirection3(val[i]);
            }

        });
    }

    public update() {
        if (this.is_game_playing == true && this.isTaskProgressTweening == false) {

            if (this.cat_info_list.length == 0) {
                this.is_game_playing = false;

                this.onGameOver();
            }
        }
    }

    private onGameOver() {
        // 保存当前自动玩状态，传递给WinUI
        const wasAutoPlaying = this.isAutoPlaying || GameUI.globalAutoPlayState;
        console.log("=== 游戏结束 ===");
        console.log("当前实例自动玩状态:", this.isAutoPlaying);
        console.log("全局自动玩状态:", GameUI.globalAutoPlayState);
        console.log("最终传递给WinUI的状态:", wasAutoPlaying);

        // 停止自动玩（但保持全局状态）
        this.stopAutoPlay();

        LocalData.lv++;
        setTimeout(() => {
            AudioMgr.playSound(AudioPath.LEVELUP);
            this.resetComboUi()
            // 传递自动玩状态给WinUI
            console.log("调用WinUI.showUI，传递参数:", wasAutoPlaying);
            WinUI.inst.showUI(wasAutoPlaying);

        }, 1000);
    }

    public onClickPause() {
        this.is_game_pause = true;
        AudioMgr.playSound(AudioPath.CLICK);
        PauseUI.inst.showUI();
    }

    useBuffItem(script: buffItemBtn) {

        switch (script.identifier) {
            case BuffItems.AddTime:
                this.onClickAddTime()
                LocalData.consumeBuffItem(BuffItems.AddTime)
                break;
            case BuffItems.Inverse:
                this.onClickResetDirection()
                LocalData.consumeBuffItem(BuffItems.Inverse)
                break;
            case BuffItems.Hint:
                this.onClickLightCat()
                LocalData.consumeBuffItem(BuffItems.Hint)
                break;
            case BuffItems.Undo:
                this.onClickRevoke()
                LocalData.consumeBuffItem(BuffItems.Undo)
                break;
            case BuffItems.SwapAnimal:
                this.swapAnimalBtn()
                LocalData.consumeBuffItem(BuffItems.SwapAnimal)
                break;

            default:
                break;
        }
        script.init()
        this.is_game_pause = false;
    }

    //刷新布局 - 快速重新排列现有动物位置
    swapAnimalBtn() {
        cc.log("刷新布局：快速重新排列现有动物位置");

        if (this.cat_info_list.length === 0) {
            cc.warn("没有动物可以重新排列");
            return;
        }

        const animalCount = this.cat_info_list.length;
        cc.log(`刷新布局：保持${animalCount}个动物不变`);

        // 保存原有动物节点
        const animalNodes = this.cat_info_list.map(info => info.cat);

        // 清理当前位置数据
        for (let i = 0; i < this.cat_info_list.length; i++) {
            for (let k = 0; k < this.cat_info_list[i].now_grids.length; k++) {
                this.grid_map.set(this.cat_info_list[i].now_grids[k], 0);
            }
        }

        // 重新生成布局数据（但保持动物数量）
        this.cat_info_list = [];
        this.grid_num_list = [];

        // 重新初始化网格
        for (let i = 0; i < this.vertical_count; i++) {
            for (let k = 0; k < this.horizontal_count; k++) {
                this.grid_num_list.push(i * 10000 + k);
                this.grid_map.set(i * 10000 + k, 0);
            }
        }

        GameUtil.randomRangeSort(this.grid_num_list);

        // 重新生成指定数量的动物布局
        let index = 0;
        let generatedCount = 0;

        while (generatedCount < animalCount && index < 10000) {
            if (this.grid_num_list.length < 2) {
                break;
            }

            GameUtil.randomRangeSort(this.dirs);
            let flag = false;

            for (let i = 0; i < 4; i++) {
                let id = this.grid_num_list[0];
                let id2 = this.getNextGrid(id, this.dirs[i]);

                if (id2 == -1 || this.grid_map.get(id2) == 1) {
                    continue;
                }

                let info = new CatInfo();
                info.dir = this.dirs[i];
                info.now_grids.push(id);
                info.now_grids.push(id2);
                info.cat = animalNodes[generatedCount]; // 使用原有节点

                this.grid_map.set(id, 1);
                this.grid_map.set(id2, 1);

                GameUtil.deleteValue(this.grid_num_list, id);
                GameUtil.deleteValue(this.grid_num_list, id2);

                this.cat_info_list.push(info);
                generatedCount++;
                flag = true;
                break;
            }

            if (!flag) {
                this.grid_num_list.shift();
            }

            index++;
        }

        // 快速更新动物位置和朝向（不重新创建节点）
        for (let i = 0; i < this.cat_info_list.length; i++) {
            let info = this.cat_info_list[i];
            let result = this.getCatPositionAndAngle(info);

            // 直接更新位置和角度
            info.cat.x = result.pos.x;
            info.cat.y = result.pos.y;
            info.cat.angle = result.angle;
            info.cat.scale = 0;
            info.cat.opacity = 255;

            // 快速缩放动画
            cc.tween(info.cat)
                .delay(i * 0.02) // 很短的延迟
                .to(0.2, { scale: 1 })
                .start();
        }

        cc.log(`刷新布局完成：快速重新排列了${this.cat_info_list.length}个动物`);
    }

    // 获取动物位置和角度（不创建节点）
    private getCatPositionAndAngle(info: CatInfo): {pos: cc.Vec2, angle: number} {
        // 使用与getCatNode相同的位置计算逻辑
        let x = Math.floor(info.now_grids[0] / 10000);
        let y = info.now_grids[0] % 10000;

        x = (x * this.ww - this.ww / 2 * this.vertical_count) + this.ww / 2;
        y = (this.ww / 2 * this.horizontal_count - y * this.ww) - this.ww / 2;

        let angle = 0;

        if (info.dir == DirectionType.up) {
            y += this.ww / 2;
            angle = 0;
        }
        else if (info.dir == DirectionType.down) {
            y -= this.ww / 2;
            angle = 180;
        }
        else if (info.dir == DirectionType.left) {
            x -= this.ww / 2;
            angle = 90;
        }
        else if (info.dir == DirectionType.right) {
            x += this.ww / 2;
            angle = 270;
        }

        return {
            pos: new cc.Vec2(x, y),
            angle: angle
        };
    }

    onAnimalCollison(animal: CatInfo) {
        let script: AnimalItem = animal.cat.getComponent('AnimalItem');
        if (script) {
            script.playCollisionAnimation()
        }
    }

    // 开始自动玩
    public startAutoPlay() {
        if (!this.is_game_playing) {
            return;
        }

        this.isAutoPlaying = true;
        GameUI.globalAutoPlayState = true; // 更新全局状态
        console.log("开始自动玩，全局状态已更新:", GameUI.globalAutoPlayState);
        this.updateAutoPlayButtonText();

        // 开始自动玩循环
        this.executeAutoPlay();
    }

    // 停止自动玩
    public stopAutoPlay() {
        console.log("停止自动玩，当前状态:", this.isAutoPlaying);
        this.isAutoPlaying = false;
        // 注意：不要在这里重置全局状态，因为我们需要在游戏结束时保持状态
        this.updateAutoPlayButtonText();

        if (this.autoPlayTimer) {
            clearTimeout(this.autoPlayTimer);
            this.autoPlayTimer = null;
        }
    }

    // 完全停止自动玩（包括全局状态）
    public stopAutoPlayCompletely() {
        console.log("完全停止自动玩");
        this.isAutoPlaying = false;
        GameUI.globalAutoPlayState = false;
        this.updateAutoPlayButtonText();

        if (this.autoPlayTimer) {
            clearTimeout(this.autoPlayTimer);
            this.autoPlayTimer = null;
        }
    }

    // 更新自动玩按钮文字
    private updateAutoPlayButtonText() {
        if (this.autoPlayBtn) {
            const label = this.autoPlayBtn.node.getChildByName("Label");
            if (label) {
                const labelComponent = label.getComponent(cc.Label);
                if (labelComponent) {
                    labelComponent.string = this.isAutoPlaying ? "停止自动" : "自动玩";
                }
            }
        }
    }

    // 执行自动玩逻辑 - 按照自动玩逻辑.md的优先级
    private executeAutoPlay() {
        if (!this.isAutoPlaying || !this.is_game_playing) {
            return;
        }

        // 检查游戏是否结束
        if (this.cat_info_list.length === 0) {
            console.log("自动玩：游戏结束，停止自动玩");
            this.stopAutoPlay();
            return;
        }

        console.log(`自动玩：开始新一轮检查，当前动物数量：${this.cat_info_list.length}`);

        // 第一优先级：找到可以成功离开的动物
        let animalsCanLeave = this.chatCatIsOut();
        console.log(`自动玩：检查可以离开的动物，找到${animalsCanLeave.length}个`);
        if (animalsCanLeave.length > 0) {
            console.log(`自动玩：找到${animalsCanLeave.length}个可以离开的动物，执行离开`);
            // 选择第一个可以离开的动物
            let selectedAnimal = animalsCanLeave[0];

            // 播放点击音效（模拟手动点击）
            AudioMgr.playSound(AudioPath.SHEEP);
            this.onMoveCat(selectedAnimal);

            // 延迟后重新开始while循环（增加思考时间）
            this.autoPlayTimer = setTimeout(() => {
                this.executeAutoPlay();
            }, 4000);
            return;
        }

        // 第二优先级：找到可以移动一段距离的动物（改变布局）
        let animalCanMove = this.findAnimalCanMove();
        if (animalCanMove) {
            console.log("自动玩：找到可以移动一段距离的动物，执行移动");

            // 播放点击音效（模拟手动点击）
            AudioMgr.playSound(AudioPath.SHEEP);
            this.onMoveCat(animalCanMove);

            // 延迟后重新开始while循环（增加思考时间）
            this.autoPlayTimer = setTimeout(() => {
                this.executeAutoPlay();
            }, 4000);
            return;
        }

        // 第三优先级：使用消除功能
        if (this.cat_info_list.length > 0) {
            console.log("自动玩：没有可移动的动物，使用消除功能");
            this.autoUseEliminateFunction();
        } else {
            // 没有动物了，停止自动玩
            console.log("自动玩：没有动物了，停止自动玩");
            this.stopAutoPlay();
        }
    }

    // 自动使用消除功能 - 模拟人为操作
    private autoUseEliminateFunction() {
        console.log("自动玩：开始消除流程 - 先显示消除UI");

        // 第一步：显示消除选择UI（模拟点击消除按钮）
        this.onClickAddTime();

        // 第二步：等待4秒后自动选择一个动物消除（模拟人为选择和思考）
        this.autoPlayTimer = setTimeout(() => {
            if (this.cat_info_list.length > 0) {
                console.log("自动玩：自动选择第一只动物进行消除");
                let firstAnimal = this.cat_info_list[0];

                // 播放点击音效（模拟手动点击）
                AudioMgr.playSound(AudioPath.SHEEP);
                // 模拟点击动物进行消除
                this.xiaochuayang(firstAnimal);

                // 等待消除完成后重新开始检查（增加思考时间）
                this.autoPlayTimer = setTimeout(() => {
                    console.log("自动玩：消除完成，重新开始检查");
                    this.executeAutoPlay();
                }, 4000);
            } else {
                console.log("自动玩：没有动物可消除，停止自动玩");
                this.stopAutoPlay();
            }
        }, 4000); // 4秒延迟，模拟人为思考和选择时间
    }

    // 查找可以移动一段距离的动物（即使不能完全离开）
    private findAnimalCanMove(): CatInfo | null {
        console.log("自动玩：检查可以移动一段距离的动物");
        for (let i = 0; i < this.cat_info_list.length; i++) {
            let info = this.cat_info_list[i];

            // 检查这个动物是否可以移动一段距离
            if (this.canAnimalMoveDistance(info)) {
                console.log(`自动玩：找到可以移动的动物，索引：${i}，方向：${info.dir}`);
                return info;
            }
        }
        console.log("自动玩：没有找到可以移动一段距离的动物");
        return null;
    }

    // 检查动物是否可以移动一段距离（参考onMoveCat的逻辑）
    private canAnimalMoveDistance(info: CatInfo): boolean {
        let i = Math.floor(info.now_grids[0] / 10000);
        let k = info.now_grids[0] % 10000;

        if (this.grid_map.get(info.now_grids[0]) == 0) {
            return false;
        }

        let add = 0;

        // 根据方向检查是否可以移动（与onMoveCat逻辑一致）
        if (info.dir == DirectionType.up) {
            k -= 1;
            while (true) {
                add += 1;
                if (k - add < 0) {
                    add = 100; // 可以完全离开
                    break;
                }
                if (this.grid_map.get(i * 10000 + (k - add)) == 1) {
                    add -= 1; // 碰撞前的距离
                    break;
                }
            }
            // 如果add > 0且不是100（完全离开），说明可以移动一段距离
            return add > 0 && add != 100;
        }
        else if (info.dir == DirectionType.down) {
            k += 1;
            while (true) {
                add += 1;
                if (k + add >= this.horizontal_count) {
                    add = 100; // 可以完全离开
                    break;
                }
                if (this.grid_map.get(i * 10000 + (k + add)) == 1) {
                    add -= 1; // 碰撞前的距离
                    break;
                }
            }
            return add > 0 && add != 100;
        }
        else if (info.dir == DirectionType.left) {
            i -= 1;
            while (true) {
                add += 1;
                if (i - add < 0) {
                    add = 100; // 可以完全离开
                    break;
                }
                if (this.grid_map.get((i - add) * 10000 + k) == 1) {
                    add -= 1; // 碰撞前的距离
                    break;
                }
            }
            return add > 0 && add != 100;
        }
        else if (info.dir == DirectionType.right) {
            i += 1;
            while (true) {
                add += 1;
                if (i + add >= this.vertical_count) {
                    add = 100; // 可以完全离开
                    break;
                }
                if (this.grid_map.get((i + add) * 10000 + k) == 1) {
                    add -= 1; // 碰撞前的距离
                    break;
                }
            }
            return add > 0 && add != 100;
        }

        return false;
    }

    /**
     * 检查VIP状态并控制自动玩按钮显示
     */
    private checkVipStatus() {
        if (this.autoPlayBtn) {
            const isVip = LocalData.isVipUser;
            this.autoPlayBtn.node.active = isVip;

            if (isVip) {
                this.updateAutoPlayButtonText();
            }
        }
    }

    /**
     * VIP状态变化事件处理
     */
    private onVipStatusChanged(event: any) {
        this.checkVipStatus();
    }

    /**
     * 销毁时清理事件监听
     */
    protected onDestroy() {
        cc.systemEvent.off('vipStatusChanged', this.onVipStatusChanged, this);
    }
}
