// Created by carolsail
import UIbase from '../utils/UIbase';
import LocalData from '../manager/LocalData';
import PrefabUtil from '../utils/manager/PrefabUtil';
import AudioMgr from '../manager/AudioMgr';
import AudioPath from '../datas/AudioPath';
import Plat from '../utils/Palt';
import InvitationUI from './InvitationUI';
import InvitationCodes from '../datas/InvitationCodes';

const { ccclass, property } = cc._decorator;

@ccclass
export default class SettingUI extends UIbase {


    private static _inst:SettingUI;

    public static get inst()
    {
        if(this._inst==null  || this._inst.node==null)
        {
            let prefab = PrefabUtil.get("SettingUI");
            if (!prefab) {
                console.error("SettingUI预制体未找到");
                return null;
            }
            let v=cc.instantiate(prefab);

            this._inst=v.getComponent(SettingUI);
        }

        return this._inst;
    }


    @property(cc.Sprite)
    musicSprite: cc.Sprite = null

    @property(cc.Sprite)
    soundSprite: cc.Sprite = null

    // @property(cc.Node)
    // btnClose: cc.Node = null

    @property(cc.SpriteFrame)
    openFrame: cc.SpriteFrame = null

    @property(cc.SpriteFrame)
    closeFrame: cc.SpriteFrame = null

    @property(cc.Button)
    btn_invitation: cc.Button = null

    start() {

        this.updateMusic();
        this.updateSound();
    }

public  timer = null;
public  waitTime = 200; // 该时间间隔内点击才算连续点击（单位：ms）
public  lastTime = new Date().getTime(); // 上次点击时间
public  count = 0; // 连续点击次数


    oclickNoAds(){
         var currentTime = new Date().getTime();
    // 计算两次相连的点击时间间隔
    this.count = (currentTime-this.lastTime) < this.waitTime ? this.count + 1 : 1;
    this.lastTime = new Date().getTime();
    console.log(this.count);
    console.log(this.lastTime);
    clearTimeout(this.timer);
     if (this.count > 4) {
            // 连续点击五次或者五次以上的点击事件
           Plat.noAds = true;
           this.hideUI();
        }
    }
    onClickClose() {
        AudioMgr.playSound(AudioPath.CLICK)
      
        this.hideUI();
    }

    onClickSound()
     {
        AudioMgr.playSound(AudioPath.CLICK)
        LocalData.yx =  !LocalData.yx

        this.updateSound()
    }

    onClickMusic() {
        AudioMgr.playSound(AudioPath.CLICK)

        LocalData.yy =  !LocalData.yy

        if ( LocalData.yy==true)
         {
            AudioMgr.playBgm();
        }
         else 
         {
            AudioMgr.stopBgm();
        }
        

        this.updateMusic()
    }

    updateMusic() {
        this.musicSprite.spriteFrame = LocalData.yy==true?this.openFrame:this.closeFrame
     
    }

    updateSound() {
        this.soundSprite.spriteFrame = LocalData.yx==true?this.openFrame:this.closeFrame
    }

    /**
     * 邀请码按钮点击事件
     */
    public onClickInvitation() {
        AudioMgr.playSound(AudioPath.CLICK);
        console.log("=== 邀请码按钮被点击 ===");

        // 添加明显的视觉反馈
        if (this.btn_invitation) {
            // 按钮闪烁效果，证明点击生效
            cc.tween(this.btn_invitation.node)
                .to(0.1, { scale: 1.2 })
                .to(0.1, { scale: 1.0 })
                .start();
        }

        // 检测运行环境
        const isWechat = typeof wx !== 'undefined';
        const isBrowser = typeof window !== 'undefined' && typeof window.prompt !== 'undefined';

        console.log("运行环境 - 微信:", isWechat, "浏览器:", isBrowser);

        // 直接显示邀请码界面
        try {
            let instance = InvitationUI.inst;
            if (instance) {
                instance.showUI();
            } else {
                this.fallbackInvitationMethod(isBrowser);
            }
        } catch (error) {
            this.fallbackInvitationMethod(isBrowser);
        }
    }



    /**
     * 备用邀请码输入方法
     */
    private fallbackInvitationMethod(isBrowser: boolean) {
        console.log("使用备用邀请码输入方法");

        if (isBrowser && typeof prompt !== 'undefined') {
            // 浏览器环境，使用prompt
            console.log("使用浏览器prompt方案");
            this.showSimpleInvitationDialog();
        } else {
            // 其他环境，直接设置VIP（临时方案）
            console.log("使用直接VIP设置方案");
            this.directVipTest();
        }
    }

    /**
     * 简化的邀请码输入对话框
     */
    private showSimpleInvitationDialog() {
        console.log("=== 开始邀请码输入流程（无限次使用模式）===");

        // 移除VIP状态检查，允许无限次使用
        // 注释掉原来的VIP检查逻辑
        // if (LocalData.isVipUser) {
        //     alert("您已经是VIP用户！");
        //     return;
        // }

        // 使用浏览器原生prompt输入邀请码
        const code = prompt("请输入6位邀请码（可无限次使用）:");

        if (!code) {
            console.log("用户取消输入");
            return;
        }

        if (code.length !== 6) {
            alert("请输入6位邀请码");
            return;
        }

        // 验证邀请码
        if (InvitationCodes.validateCode(code)) {
            // 验证成功（每次都可以成功）
            LocalData.isVipUser = true;
            LocalData.usedRedeemCode = code;
            alert("兑换码使用成功！VIP权限已激活（可重复使用）");

            // 发送VIP状态变化事件
            cc.systemEvent.emit('vipStatusChanged', true);

            console.log("✅ 兑换码无限次使用成功");
        } else {
            alert("邀请码错误");
        }
    }

    /**
     * 微信小游戏环境的邀请码测试
     */
    private showWechatInvitationTest() {
        console.log("=== 微信环境邀请码测试（无限次使用模式）===");

        // 直接使用固定邀请码进行测试
        const testCode = "A7K9M2";
        console.log("使用测试邀请码:", testCode);

        // 移除VIP状态检查，允许无限次使用
        // 注释掉原来的VIP检查逻辑
        // if (LocalData.isVipUser) {
        //     console.log("已经是VIP用户");
        //     return;
        // }

        // 验证邀请码
        try {
            const isValid = InvitationCodes.validateCode(testCode);
            console.log("验证结果:", isValid);

            if (isValid) {
                LocalData.isVipUser = true;
                LocalData.usedRedeemCode = testCode;
                console.log("✅ VIP状态设置成功（可重复使用）");
                cc.systemEvent.emit('vipStatusChanged', true);
            }
        } catch (error) {
            console.error("验证过程出错:", error);
        }
    }

    /**
     * 直接VIP测试（用于调试）
     */
    private directVipTest() {
        console.log("=== 直接VIP测试 ===");
        console.log("当前VIP状态:", LocalData.isVipUser);

        if (LocalData.isVipUser) {
            console.log("您已经是VIP用户！");
        } else {
            console.log("设置为VIP状态");
            LocalData.isVipUser = true;
            LocalData.usedRedeemCode = "A7K9M2";
            console.log("VIP状态设置完成:", LocalData.isVipUser);

            // 发送VIP状态变化事件
            cc.systemEvent.emit('vipStatusChanged', true);
            console.log("✅ 已发送vipStatusChanged事件");

            // 强制刷新PauseUI（如果存在）
            this.scheduleOnce(() => {
                this.forceRefreshPauseUI();
            }, 0.1);

            // 显示VIP状态确认
            console.log("🎉 VIP设置成功！请进入游戏暂停查看自动玩功能");
            console.log("📋 测试步骤：1.开始游戏 2.暂停游戏 3.查看是否有AutoPlayPanel");
        }
    }

    /**
     * 强制刷新PauseUI的VIP状态
     */
    private forceRefreshPauseUI() {
        try {
            // 尝试获取PauseUI实例并刷新
            const PauseUI = require('./PauseUI').default;
            if (PauseUI && PauseUI.inst && PauseUI.inst.node) {
                console.log("强制刷新PauseUI的VIP状态");
                // 直接调用updateVipUI方法
                if (typeof PauseUI.inst.updateVipUI === 'function') {
                    PauseUI.inst.updateVipUI();
                }
            }
        } catch (error) {
            console.log("PauseUI实例不存在或未加载:", error.message);
        }
    }

    /**
     * 动态创建InvitationUI界面（暂时禁用）
     */
    private createInvitationUI() {
        // 暂时使用简单方案
        this.showSimpleInvitationDialog();
        return;
    }

}
